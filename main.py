from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
import time
import os
import platform
import undetected_chromedriver as uc
from selenium_stealth import stealth
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_driver():
    """创建新的Chrome驱动实例"""
    chrome_options = webdriver.ChromeOptions()

    # 根据操作系统设置Chrome二进制文件路径
    system = platform.system().lower()
    if system == "linux":
        chrome_options.binary_location = os.path.join(os.getcwd(), "chrome", "chrome-linux64", "chrome")
    else:  # Windows
        chrome_options.binary_location = os.path.join(os.getcwd(), "chrome", "chrome-win64", "chrome.exe")

    # 不使用无头模式，保持网页打开状态
    # chrome_options.add_argument('--headless')  # 注释掉无头模式，这样可以看到浏览器操作
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    chrome_options.add_argument('--window-size=1920,1080')

    # 检查是否设置了代理环境变量
    proxy = os.getenv('proxy')
    if proxy:
        logger.info(f"使用代理: {proxy}")
        chrome_options.add_argument(f'--proxy-server={proxy}')

    prefs = {"profile.managed_default_content_settings.images": 2}
    chrome_options.add_experimental_option("prefs", prefs)

    try:
        driver = uc.Chrome(options=chrome_options)
    except Exception as e:
        logger.error(f"创建Chrome驱动失败: {e}")
        # 如果undetected_chromedriver失败，尝试使用普通的webdriver
        driver = webdriver.Chrome(options=chrome_options)

    stealth(driver,
            languages=["en-US", "en"],
            vendor="Google Inc.",
            platform="Win32",
            webgl_vendor="Google Inc. (NVIDIA)",
            renderer="ANGLE (NVIDIA, NVIDIA GeForce RTX 4060 (0x00002882) Direct3D11 vs_5_0 ps_5_0, D3D11)",
            fix_hairline=True,
            )

    driver.execute_cdp_cmd('Network.setBlockedURLs', {
        'urls': ['.css', '*.css', 'https://*.css', 'http://*.css', '.woff2', '*.woff2', 'https://*.woff2',
                 'http://*.woff2']
    })
    driver.execute_cdp_cmd('Network.enable', {})

    return driver

def genspark_automation():
    """执行Genspark自动化操作"""
    driver = None
    try:
        logger.info("开始创建Chrome驱动...")
        driver = create_driver()

        # 1. 打开登录页面
        login_url = "https://www.genspark.ai/api/login?redirect_url=%2F"
        logger.info(f"正在打开登录页面: {login_url}")
        driver.get(login_url)

        # 等待页面加载
        time.sleep(2)

        # 2. 点击登录按钮
        login_button_xpath = "/html/body/div[3]/div[3]/div/div[1]/div/div/div/div[2]/div[2]/div[1]/button"
        logger.info("正在查找并点击登录按钮...")

        wait = WebDriverWait(driver, 20)

        # 3. 找到注册链接并获取href
        register_link_xpath = "/html/body/div[3]/div[3]/div/div[1]/div/div/div/form/div[5]/p/a"
        logger.info("正在查找注册链接...")

        try:
            register_link = wait.until(EC.presence_of_element_located((By.XPATH, register_link_xpath)))
            register_href = register_link.get_attribute("href")
            logger.info(f"找到注册链接: {register_href}")

            # 4. 跳转到注册页面
            logger.info("正在跳转到注册页面...")
            driver.get(register_href)
            time.sleep(2)
            logger.info("成功跳转到注册页面")

            # 5. 查找指定XPath的组件并获取src值
            target_xpath = "/html/body/div[2]/div/div/div/div/div[1]/form/div[5]/ul/li[1]/div/div[2]/div[4]/img"
            logger.info(f"正在查找XPath为 {target_xpath} 的组件...")

            try:
                # 等待元素出现
                target_element = wait.until(EC.presence_of_element_located((By.XPATH, target_xpath)))
                src_value = target_element.get_attribute("src")
                logger.info(f"找到目标组件，src值为: {src_value}")
                print(f"组件src值: {src_value}")

            except TimeoutException:
                logger.error(f"找不到XPath为 {target_xpath} 的组件")
                print("未找到指定的组件")
            except Exception as e:
                logger.error(f"获取src值时发生错误: {e}")
                print(f"获取src值失败: {e}")

        except TimeoutException:
            logger.error("找不到注册链接")
            return

        # 6. 等待999秒
        logger.info("开始等待999秒...")
        time.sleep(999)
        logger.info("等待完成")

    except Exception as e:
        logger.error(f"执行过程中发生错误: {e}")
    finally:
        if driver:
            logger.info("关闭浏览器...")
            driver.quit()

if __name__ == "__main__":
    genspark_automation()